import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import 'package:easy_localization/easy_localization.dart';

class LandingPage extends StatefulWidget {
  const LandingPage({super.key});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  late YoutubePlayerController _youtubeController;
  bool _hasAutoDetected = false;

  @override
  void initState() {
    super.initState();
    _youtubeController = YoutubePlayerController.fromVideoId(
      videoId: 'H9UEXTKkQ34',
      autoPlay: false,
      params: const YoutubePlayerParams(
        mute: false,
        showControls: true,
        showFullscreenButton: true,
        enableCaption: true,
        captionLanguage: 'en',
      ),
    );

    // Auto-detect browser language for web
    if (kIsWeb && !_hasAutoDetected) {
      _autoDetectLanguage();
    }
  }

  void _autoDetectLanguage() {
    if (!kIsWeb) return;

    try {
      // Get browser language
      final browserLanguage = context.deviceLocale.languageCode;
      final supportedLanguages = ['en', 'es', 'fr', 'de', 'zh', 'ja', 'ar'];

      if (supportedLanguages.contains(browserLanguage)) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.setLocale(Locale(browserLanguage)).then((_) {
            setState(() {
              _hasAutoDetected = true;
            });
          });
        });
      }
    } catch (e) {
      debugPrint('Auto-detection failed: $e');
    }
  }

  @override
  void dispose() {
    _youtubeController.close();
    super.dispose();
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    final isLoggedIn = user != null && user.emailVerified;

    // For web, wrap the entire scaffold in a widget that rebuilds on locale changes
    if (kIsWeb) {
      return Builder(
        key: ValueKey(context.locale.languageCode),
        builder: (context) => _buildScaffold(context, isLoggedIn),
      );
    }

    return _buildScaffold(context, isLoggedIn);
  }

  Widget _buildScaffold(BuildContext context, bool isLoggedIn) {
    return Scaffold(
      backgroundColor: const Color(0xFF0f172a),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Hero Section
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: const [
                    Color.fromARGB(255, 129, 56, 123),
                    Color.fromARGB(255, 34, 57, 88),
                    Color.fromARGB(255, 107, 94, 182),
                  ],
                  transform: GradientRotation(
                    (DateTime.now().millisecondsSinceEpoch / 3000) %
                        (2 * 3.14159),
                  ),
                ),
              ),
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 80,
                  ),
                  child: Column(
                    children: [
                      // Language Selector (Web only)
                      if (kIsWeb) ...[
                        Align(
                          alignment: Alignment.topRight,
                          child: _buildLanguageSelector(),
                        ),
                        const SizedBox(height: 20),
                      ],
                      // Logo and Title
                      Center(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color.fromARGB(255, 177, 175, 175),
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              'assets/images/money_mouth.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 32),
                      Text(
                        kIsWeb ? () {
                          final currentLocale = context.locale.languageCode;
                          debugPrint('Current locale for app_title: $currentLocale');
                          return 'Money Mouthy ($currentLocale)';
                        }() : 'Money Mouthy',
                        style: const TextStyle(
                          fontSize: 56,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: -1,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        kIsWeb
                            ? () {
                                final currentLocale =
                                    context.locale.languageCode;
                                debugPrint(
                                    'Current locale for tagline: $currentLocale');
                                switch (currentLocale) {
                                  case 'es':
                                    return 'Pon Tu Dinero Donde Está Tu Boca';
                                  case 'fr':
                                    return 'Mettez Votre Argent Là Où Est Votre Bouche';
                                  case 'de':
                                    return 'Setzen Sie Ihr Geld Dort Ein, Wo Ihr Mund Ist';
                                  case 'zh':
                                    return '把你的钱放在你的嘴上';
                                  case 'ja':
                                    return 'お金を口に出すところに置く';
                                  case 'ar':
                                    return 'ضع أموالك حيث فمك';
                                  default:
                                    return 'Put Your Money Where Your Mouth Is';
                                }
                              }()
                            : 'Put Your Money Where Your Mouth Is',
                        style: const TextStyle(
                          fontSize: 24,
                          color: Colors.white70,
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF5159FF).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(50),
                          border: Border.all(
                            color: const Color(
                              0xFF5159FF,
                            ).withOpacity(0.3),
                          ),
                        ),
                        child: const Text(
                          '💰 Your opinion has minimum value - \$0.05 more! 💰',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 48),

                      // Action Buttons
                      if (isLoggedIn) ...[
                        _buildPrimaryButton(
                          kIsWeb ? context.tr('go_to_home') : 'Go to Home',
                          Icons.home,
                          () =>
                              Navigator.pushReplacementNamed(context, '/home'),
                        ),
                      ] else ...[
                        Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          alignment: WrapAlignment.center,
                          children: [
                            _buildPrimaryButton(
                              kIsWeb
                                  ? context.tr('get_started')
                                  : 'Get Started',
                              Icons.rocket_launch,
                              () => Navigator.pushNamed(context, '/signup'),
                            ),
                            _buildSecondaryButton(
                              kIsWeb ? context.tr('sign_in') : 'Sign In',
                              Icons.login,
                              () => Navigator.pushNamed(context, '/login'),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Features Section
            Container(
              width: double.infinity,
              color: Colors.white,
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 40,
                  ),
                  child: Column(
                    children: [
                      // Download Buttons (moved from bottom)
                      Wrap(
                        spacing: 16,
                        runSpacing: 16,
                        alignment: WrapAlignment.center,
                        children: [
                          _buildOutlinedDownloadButton(
                            kIsWeb
                                ? context.tr('download_android')
                                : 'Download for Android',
                            Icons.android,
                            () => _launchURL(
                              '/downloads/moneymouthy-android.apk',
                            ),
                          ),
                          _buildOutlinedDownloadButton(
                            kIsWeb
                                ? context.tr('download_ios')
                                : 'Download for iOS',
                            Icons.apple,
                            () => _launchURL('/downloads/moneymouthy-ios.ipa'),
                          ),
                        ],
                      ),

                      // YouTube Video Section
                      const SizedBox(height: 32),

                      _buildYouTubeVideoSection(),
                      // const SizedBox(height: 32),
                      Text(
                        kIsWeb
                            ? context.tr('why_money_mouthy')
                            : 'Why Money Mouthy?',
                        style: const TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0f172a),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 48),
                      Wrap(
                        spacing: 32,
                        runSpacing: 32,
                        alignment: WrapAlignment.center,
                        children: [
                          _buildFeatureCard(
                            Icons.monetization_on,
                            kIsWeb ? context.tr('earn_money') : 'Earn Money',
                            kIsWeb
                                ? context.tr('earn_money_desc')
                                : 'Get paid for your opinions and posts',
                          ),
                          _buildFeatureCard(
                            Icons.people,
                            kIsWeb ? context.tr('connect') : 'Connect',
                            kIsWeb
                                ? context.tr('connect_desc')
                                : 'Join a community of like-minded people',
                          ),
                          _buildFeatureCard(
                            Icons.trending_up,
                            kIsWeb ? context.tr('grow') : 'Grow',
                            kIsWeb
                                ? context.tr('grow_desc')
                                : 'Build your following and influence',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Navigation Links Section
            Container(
              width: double.infinity,
              color: const Color(0xFF0f172a),
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 1200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 40,
                  ),
                  child: Wrap(
                    spacing: 24,
                    runSpacing: 16,
                    alignment: WrapAlignment.center,
                    children: [
                      TextButton(
                        onPressed: () =>
                            Navigator.pushNamed(context, '/privacy'),
                        child: Text(
                          kIsWeb
                              ? context.tr('privacy_policy')
                              : 'Privacy Policy',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pushNamed(context, '/terms'),
                        child: Text(
                          kIsWeb
                              ? context.tr('terms_of_service')
                              : 'Terms of Service',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pushNamed(context, '/about'),
                        child: Text(
                          kIsWeb ? context.tr('about_us') : 'About Us',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                      TextButton(
                        onPressed: () =>
                            Navigator.pushNamed(context, '/support'),
                        child: Text(
                          kIsWeb ? context.tr('support') : 'Support',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Footer
            Container(
              width: double.infinity,
              color: const Color(0xFF0f172a),
              padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 24),
              child: const Center(
                child: Text(
                  '© 2025 Money Mouthy. All rights reserved.',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrimaryButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF5159FF),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        elevation: 0,
      ),
    );
  }

  Widget _buildSecondaryButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        side: const BorderSide(color: Colors.white, width: 2),
      ),
    );
  }

  Widget _buildFeatureCard(IconData icon, String title, String description) {
    return Container(
      width: 300,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF5159FF).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: 32, color: const Color(0xFF5159FF)),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF0f172a),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOutlinedDownloadButton(
    String text,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(
        text,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.black,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        side: const BorderSide(color: Colors.black, width: 1),
      ),
    );
  }

  Widget _buildYouTubeVideoSection() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 600),
      child: Column(
        children: [
          const Text(
            'See Money Mouthy in Action',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF0f172a),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: YoutubePlayer(
                controller: _youtubeController,
                aspectRatio: 16 / 9,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Learn how Money Mouthy works',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector() {
    if (!kIsWeb) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<Locale>(
          value: context.locale,
          icon: const Icon(Icons.language, color: Colors.white, size: 20),
          dropdownColor: const Color(0xFF0f172a),
          style: const TextStyle(color: Colors.white),
          items: [
            DropdownMenuItem(
              value: context.deviceLocale,
              child:
                  const Text('🌐 Auto', style: TextStyle(color: Colors.white)),
            ),
            const DropdownMenuItem(
              value: Locale('en'),
              child:
                  Text('🇺🇸 English', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('es'),
              child:
                  Text('🇪🇸 Español', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('fr'),
              child:
                  Text('🇫🇷 Français', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('de'),
              child:
                  Text('🇩🇪 Deutsch', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('zh'),
              child: Text('🇨🇳 中文', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('ja'),
              child: Text('🇯🇵 日本語', style: TextStyle(color: Colors.white)),
            ),
            DropdownMenuItem(
              value: Locale('ar'),
              child:
                  Text('🇸🇦 العربية', style: TextStyle(color: Colors.white)),
            ),
          ],
          onChanged: (Locale? locale) {
            if (locale != null) {
              debugPrint('Changing locale to: ${locale.languageCode}');
              if (locale == context.deviceLocale) {
                // Auto-detect language
                _autoDetectLanguage();
              } else {
                context.setLocale(locale).then((_) {
                  if (mounted) {
                    debugPrint(
                        'Locale changed successfully to: ${locale.languageCode}');
                    // Force rebuild after locale change
                    setState(() {});
                  }
                });
              }
            }
          },
        ),
      ),
    );
  }
}
